#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
概念文件读取测试脚本
用于诊断 dynamic_gap_detector.py 中概念板块资金流入为什么是0的问题
"""

import pandas as pd
import os
from datetime import datetime, time

# 从 dynamic_gap_detector.py 导入相关函数
from dynamic_gap_detector import (
    classify_file_type, 
    find_latest_file, 
    convert_to_float,
    extract_timestamp_from_filename
)

def test_concept_file_reading():
    """测试概念文件读取逻辑"""
    
    # 测试数据目录
    data_dir = r'D:\dev\mootdx\adata\Gemini\fund_data\2025-08-01'
    
    if not os.path.exists(data_dir):
        print(f"❌ 数据目录不存在: {data_dir}")
        return
    
    print(f"🔍 测试数据目录: {data_dir}")
    
    # 1. 扫描所有文件
    all_files = os.listdir(data_dir)
    print(f"📁 目录中共有 {len(all_files)} 个文件")
    
    # 2. 筛选概念文件
    concept_files = []
    for f in all_files:
        if f.endswith('.csv'):
            file_type = classify_file_type(f)
            if file_type == 'concept':
                concept_files.append(f)
    
    print(f"📊 找到 {len(concept_files)} 个概念文件:")
    for f in concept_files:
        print(f"  - {f}")
    
    if not concept_files:
        print("❌ 没有找到概念文件！")
        return
    
    # 3. 测试时间戳提取
    print(f"\n⏰ 测试时间戳提取:")
    for f in concept_files:
        timestamp = extract_timestamp_from_filename(f)
        print(f"  {f} -> {timestamp}")
    
    # 4. 测试文件选择逻辑 - 专门测试问题时间点
    test_times = [
        time(9, 31, 45),  # 09:31:45 - 你报告的问题时间点
    ]
    
    for test_time in test_times:
        print(f"\n🕐 测试时间点: {test_time}")
        latest_file = find_latest_file(concept_files, test_time)
        print(f"  选择的文件: {latest_file}")
        
        if latest_file:
            # 5. 测试文件读取和处理
            test_file_processing(data_dir, latest_file)

def test_file_processing(data_dir, filename):
    """测试单个文件的处理逻辑"""
    
    file_path = os.path.join(data_dir, filename)
    print(f"\n📖 测试文件处理: {filename}")
    
    try:
        # 读取文件
        df = pd.read_csv(file_path, encoding='utf-8-sig', on_bad_lines='skip')
        print(f"  ✅ 文件读取成功，形状: {df.shape}")
        print(f"  📋 原始列名: {list(df.columns)}")
        
        if not df.empty:
            print(f"  📄 前3行数据:")
            print(df.head(3).to_string())
        
        # 检查必要列
        has_name = '名称' in df.columns
        has_inflow = '今日主力净流入-净额' in df.columns
        
        print(f"\n  🔍 列检查:")
        print(f"    名称列存在: {has_name}")
        print(f"    主力净流入列存在: {has_inflow}")
        
        if not has_name:
            # 尝试名称列映射
            name_candidates = ['名称', '概念名称', '板块名称', '行业', 'name', 'concept_name']
            for candidate in name_candidates:
                if candidate in df.columns:
                    print(f"    可映射名称列: {candidate}")
                    break
        
        if not has_inflow:
            # 尝试主力净流入列映射
            inflow_candidates = [
                '今日主力净流入-净额', '主力净流入', '净流入', '净额',
                '今日主力净流入', '主力资金净流入', '资金净流入',
                'main_net_inflow', 'net_inflow', 'inflow_amount'
            ]
            for candidate in inflow_candidates:
                if candidate in df.columns:
                    print(f"    可映射主力净流入列: {candidate}")
                    break
        
        # 如果有必要列，测试数据转换
        if has_name and has_inflow:
            print(f"\n  🔄 测试数据转换:")
            
            # 检查原始数据
            sample_values = df['今日主力净流入-净额'].head(5)
            print(f"    原始样本值: {sample_values.tolist()}")
            print(f"    原始数据类型: {sample_values.dtype}")
            
            # 转换数据
            df['今日主力净流入-净额'] = df['今日主力净流入-净额'].apply(convert_to_float)
            converted_values = df['今日主力净流入-净额'].head(5)
            print(f"    转换后样本值: {converted_values.tolist()}")
            
            # 统计
            positive_count = (df['今日主力净流入-净额'] > 0).sum()
            zero_count = (df['今日主力净流入-净额'] == 0).sum()
            negative_count = (df['今日主力净流入-净额'] < 0).sum()
            
            print(f"    正值数量: {positive_count}")
            print(f"    零值数量: {zero_count}")
            print(f"    负值数量: {negative_count}")
            
            if positive_count > 0:
                top_5 = df[df['今日主力净流入-净额'] > 0].nlargest(5, '今日主力净流入-净额')
                print(f"    Top 5 正流入:")
                for idx, row in top_5.iterrows():
                    print(f"      {row['名称']}: {row['今日主力净流入-净额']:,.0f}")
            else:
                print(f"    ⚠️ 没有正流入数据！")
        
    except Exception as e:
        print(f"  ❌ 文件处理失败: {e}")
        import traceback
        traceback.print_exc()

def test_actual_processing_logic():
    """测试实际的概念文件处理逻辑，模拟 dynamic_gap_detector.py 中的处理流程"""

    data_dir = r'D:\dev\mootdx\adata\Gemini\fund_data\2025-08-01'
    current_sim_time = time(9, 31, 45)  # 问题时间点

    print(f"\n🔍 测试实际处理逻辑 - 时间点: {current_sim_time}")

    # 1. 扫描和分类文件（模拟 dynamic_gap_detector.py 的逻辑）
    all_files = os.listdir(data_dir)
    concept_files = []

    for f in all_files:
        if f.endswith('.csv'):
            file_type = classify_file_type(f)
            if file_type == 'concept':
                concept_files.append(f)

    concept_files = sorted(concept_files)
    print(f"📊 找到 {len(concept_files)} 个概念文件")

    # 2. 选择最新文件
    latest_concept_file = find_latest_file(concept_files, current_sim_time)
    print(f"🎯 选择的概念文件: {latest_concept_file}")

    if not latest_concept_file:
        print("❌ 没有找到合适的概念文件！")
        return

    # 3. 模拟完整的处理流程
    try:
        df_con = pd.read_csv(os.path.join(data_dir, latest_concept_file), encoding='utf-8-sig', on_bad_lines='skip')
        print(f"✅ 文件读取成功，形状: {df_con.shape}")

        # 4. 列名映射逻辑
        rename_needed = False
        rename_map = {}

        # 名称列映射
        name_candidates = ['名称', '概念名称', '板块名称', '行业', 'name', 'concept_name']
        if '名称' not in df_con.columns:
            for candidate in name_candidates:
                if candidate in df_con.columns:
                    rename_map[candidate] = '名称'
                    rename_needed = True
                    break

        # 主力净流入列映射
        inflow_candidates = [
            '今日主力净流入-净额', '主力净流入', '净流入', '净额',
            '今日主力净流入', '主力资金净流入', '资金净流入',
            'main_net_inflow', 'net_inflow', 'inflow_amount'
        ]
        if '今日主力净流入-净额' not in df_con.columns:
            for candidate in inflow_candidates:
                if candidate in df_con.columns:
                    rename_map[candidate] = '今日主力净流入-净额'
                    rename_needed = True
                    break

        if rename_needed:
            df_con.rename(columns=rename_map, inplace=True)
            print(f"🔄 列名映射: {rename_map}")

        # 5. 检查必要列
        if '名称' in df_con.columns and '今日主力净流入-净额' in df_con.columns:
            print("✅ 必要列检查通过")

            # 6. 数据清理和转换
            df_con = df_con.dropna(subset=['名称', '今日主力净流入-净额'])
            print(f"📊 清理后数据形状: {df_con.shape}")

            # 7. 数据转换
            original_sample = df_con['今日主力净流入-净额'].iloc[0] if len(df_con) > 0 else 0
            print(f"📊 转换前样本值: {original_sample} (类型: {type(original_sample)})")

            df_con['今日主力净流入-净额'] = df_con['今日主力净流入-净额'].apply(convert_to_float)
            converted_sample = df_con['今日主力净流入-净额'].iloc[0] if len(df_con) > 0 else 0
            print(f"📊 转换后样本值: {converted_sample}")

            # 8. 单位检测和转换
            if converted_sample != 0:
                if abs(converted_sample) < 1000:  # 可能是亿元单位
                    df_con['今日主力净流入-净额'] = df_con['今日主力净流入-净额'] * 1e8
                    print(f"🔄 检测到亿元单位，已转换为元")
                elif abs(converted_sample) < 100000:  # 可能是万元单位
                    df_con['今日主力净流入-净额'] = df_con['今日主力净流入-净额'] * 1e4
                    print(f"🔄 检测到万元单位，已转换为元")

            # 9. 添加类型标记
            df_con['type'] = '概念'

            # 10. 过滤有效数据
            valid_data = df_con[df_con['今日主力净流入-净额'].notna()]
            print(f"📊 有效数据数量: {len(valid_data)}")

            if not valid_data.empty:
                # 11. 模拟后续处理
                all_sectors_list = [valid_data[['名称', '今日主力净流入-净额', 'type']]]
                all_sectors_df = pd.concat(all_sectors_list, ignore_index=True).drop_duplicates(subset=['名称'])
                all_sectors_df['名称'] = all_sectors_df['名称'].str.strip()
                all_sectors_df['今日主力净流入-净额'] = all_sectors_df['今日主力净流入-净额'].apply(convert_to_float)
                all_sectors_df.dropna(subset=['今日主力净流入-净额'], inplace=True)

                print(f"📊 最终处理后数据数量: {len(all_sectors_df)}")

                # 12. 应用概念过滤逻辑（这里可能是问题所在）
                print(f"📊 过滤前数据数量: {len(all_sectors_df)}")

                # 模拟 dynamic_gap_detector.py 中的过滤逻辑
                from concept_sector_filter import get_meaningless_items

                if not all_sectors_df.empty:
                    concept_mask = all_sectors_df['type'] == '概念'
                    if concept_mask.any():
                        meaningless_items = get_meaningless_items()
                        print(f"📊 无意义概念列表: {sorted(list(meaningless_items))}")

                        # 检查哪些概念会被过滤
                        concepts_to_filter = all_sectors_df[concept_mask]['名称'].isin(meaningless_items)
                        filtered_concepts = all_sectors_df[concept_mask & concepts_to_filter]['名称'].tolist()
                        if filtered_concepts:
                            print(f"⚠️ 将被过滤的概念: {filtered_concepts}")

                        meaningful_concept_mask = ~all_sectors_df['名称'].isin(meaningless_items)
                        all_sectors_df = all_sectors_df[~concept_mask | meaningful_concept_mask]
                        print(f"📊 过滤后数据数量: {len(all_sectors_df)}")

                # 13. 检查正流入数据
                positive_flow_df = all_sectors_df[all_sectors_df['今日主力净流入-净额'] > 0]
                print(f"📊 正流入数据数量: {len(positive_flow_df)}")

                if len(positive_flow_df) > 0:
                    print(f"📊 Top 5 正流入概念:")
                    top_5 = positive_flow_df.nlargest(5, '今日主力净流入-净额')
                    for idx, row in top_5.iterrows():
                        print(f"  {row['名称']}: {row['今日主力净流入-净额']:,.0f}")
                else:
                    print("❌ 没有正流入数据！这就是问题所在！")

                    # 检查所有数据的分布
                    print(f"📊 数据分布:")
                    print(f"  总数: {len(all_sectors_df)}")
                    print(f"  正值: {(all_sectors_df['今日主力净流入-净额'] > 0).sum()}")
                    print(f"  零值: {(all_sectors_df['今日主力净流入-净额'] == 0).sum()}")
                    print(f"  负值: {(all_sectors_df['今日主力净流入-净额'] < 0).sum()}")

                    # 显示一些样本数据
                    print(f"📊 样本数据:")
                    print(all_sectors_df[['名称', '今日主力净流入-净额']].head(10))
            else:
                print("❌ 处理后无有效数据")
        else:
            missing_cols = []
            if '名称' not in df_con.columns:
                missing_cols.append('名称')
            if '今日主力净流入-净额' not in df_con.columns:
                missing_cols.append('今日主力净流入-净额')
            print(f"❌ 缺少必要列: {missing_cols}")
            print(f"📋 可用列: {list(df_con.columns)}")

    except Exception as e:
        print(f"❌ 处理失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_concept_file_reading()
    test_actual_processing_logic()
